/* eslint-disable */
import React, { useEffect, useState, useMemo, useCallback, useRef, memo } from "react";
import "video.js/dist/video-js.css";
import { Grid, Typography, Accordion, AccordionDetails, AccordionSummary, Skeleton, Divider, IconButton, Box, Tooltip, Button } from "@mui/material";
import { KeyboardArrowDown, FilterAlt, FilterAltOff } from "@mui/icons-material";
import { VariableSizeList as List } from "react-window";
import dayjs from "dayjs";
// import utc from "dayjs/plugin/utc";
// import timezone from "dayjs/plugin/timezone";
import { defaultValues, CUTOFF_DATE, userValues } from "../../../utils";
import Detection from "./Detection";
import Filters from "./Filters";
import { useApp } from "../../../hooks/AppHook";
import idb from "../../../indexedDB";
import theme from "../../../theme";
import { useUser } from "../../../hooks/UserHook.jsx";
import s3Controller from "../../../controllers/S3.controller";

// dayjs.extend(utc);
// dayjs.extend(timezone);

const Events = memo(
    ({
        selectedStream,
        loadedArtifacts,
        setLoadedArtifacts,
        fetchMoreArtifacts,
        setFavouriteArtifacts,
        isLoadingMoreArtifacts,
        isLoadingArtifacts,
        artifactIndicator,
        viewedArtifacts,
        setViewedArtifacts,
        favouriteArtifacts = [],
        setArtifactIndicator,
    }) => {
        const [expanded, setExpanded] = useState();
        const { screenSize, deviceHeight, timezone } = useApp();

        const [artifacts, setArtifacts] = useState([]);
        const [category, setCategory] = useState("All");
        const [categories, setCategories] = useState(["All"]);
        const [startTimestamp, setStartTimestamp] = useState(CUTOFF_DATE);
        const [endTimestamp, setEndTimestamp] = useState(dayjs());
        const [isLoadingMore, setIsLoadingMore] = useState(false);
        const [showLoadMoreButton, setShowLoadMoreButton] = useState(false);
        const [signedUrls, setSignedUrls] = useState(new Map());
        const listRef = useRef();
        const { user } = useUser();

        useEffect(() => {
            if (!artifacts) return;
            setCategories(
                ["All"].concat(
                    artifacts.reduce(
                        (arr, { super_category }) => (!super_category || arr.includes(super_category) ? arr : arr.concat(super_category)),
                        [],
                    ),
                ),
            );
        }, [artifacts]);

        useEffect(() => {
            if (loadedArtifacts && loadedArtifacts.length > 0) {
                setArtifacts(loadedArtifacts);

                const fetchSignedUrls = async () => {
                    try {
                        const newSignedUrls = await s3Controller.fetchSignedUrlsBatch(loadedArtifacts);
                        setSignedUrls(newSignedUrls);
                    } catch (error) {
                        console.error("[Stream Events] Error fetching signed URLs batch:", error);
                    }
                };

                fetchSignedUrls();
            }
        }, [loadedArtifacts]);

        const filteredArtifacts = useMemo(() => {
            if (!artifacts) return [];
            return artifacts
                .filter((a) => (category === "All" ? true : a.super_category?.toLowerCase() === category.toLowerCase()))
                .filter((a) => dayjs(a.timestamp).valueOf() >= startTimestamp.valueOf() && dayjs(a.timestamp).valueOf() <= endTimestamp.valueOf()) // filter for time range
                .sort((a, b) => dayjs(b.timestamp).valueOf() - dayjs(a.timestamp).valueOf());
        }, [artifacts, category, startTimestamp, endTimestamp]);

        const getRowHeight = useCallback((index) => (expanded === index ? (screenSize.xs ? 340 : 220) : 50), [expanded]);

        const handleScroll = useCallback(
            ({ scrollOffset, scrollDirection }) => {
                if (listRef.current) {
                    const listHeight = listRef.current.props.height;
                    const totalHeight = filteredArtifacts.reduce((acc, _, index) => acc + getRowHeight(index), 0);
                    const isBottom = scrollOffset + listHeight >= totalHeight - 50;
                    if (isBottom && scrollDirection === "forward" && !isLoadingMoreArtifacts) {
                        if (filteredArtifacts.length < 9) {
                            setShowLoadMoreButton(isBottom && scrollDirection === "forward");
                        } else {
                            handleLoadMore();
                        }
                    }
                }
            },
            [filteredArtifacts, getRowHeight],
        );

        const handleItemsRendered = useCallback(
            ({ overscanStopIndex }) => {
                if (category !== "All") return setShowLoadMoreButton(false);
                if (overscanStopIndex === filteredArtifacts.length - 1 && fetchMoreArtifacts && !isLoadingMore) {
                    setShowLoadMoreButton(true);
                }
            },
            [filteredArtifacts, fetchMoreArtifacts],
        );

        const updateArtifactStorage = async (artifact) => {
            try {
                const storeName = `${selectedStream.VesselId}_artifact`;
                const data = await idb.getItems(storeName, (item) => item._id === artifact._id);
                if (data.length > 0) {
                    await idb.updateItem(storeName, artifact._id, { isView: true });
                }
                setViewedArtifacts((v) => [...v, artifact._id]);
                setArtifactIndicator((prev) => {
                    const currentList = prev[selectedStream.VesselId];
                    if (!currentList) {
                        return prev;
                    }
                    const filtered = currentList.filter((item) => item._id !== artifact._id);
                    if (filtered.length === 0) {
                        // If the filtered array is empty, remove the key

                        const { [selectedStream.VesselId]: _, ...rest } = prev;
                        return rest;
                    }
                    return {
                        ...prev,
                        [selectedStream.VesselId]: filtered,
                    };
                });
            } catch (error) {
                console.error("Error fetching data from storage:", error);
            }
        };

        const handleAccordionChange = (index) => {
            updateArtifactStorage(filteredArtifacts[index]);
            setExpanded(expanded === index ? undefined : index);
            if (listRef.current) {
                listRef.current.resetAfterIndex(index);
            }
        };

        const RenderRow = ({ index, style }) => {
            const artifact = filteredArtifacts[index];
            const rowRef = useRef();
            useEffect(() => {
                if (rowRef.current) {
                    listRef.current.resetAfterIndex(index);
                }
            }, [rowRef, index]);

            const isViewed = artifactIndicator[selectedStream.VesselId]
                ? artifactIndicator[selectedStream.VesselId].find((a) => a._id === artifact._id) && !viewedArtifacts.includes(artifact._id)
                    ? false
                    : true
                : true;

            return (
                <div style={style} ref={rowRef}>
                    <Accordion
                        expanded={expanded === index}
                        onChange={() => handleAccordionChange(index)}
                        disableGutters
                        sx={{ bgcolor: isViewed ? undefined : "#282C39" }}
                    >
                        <AccordionSummary expandIcon={<KeyboardArrowDown />}>
                            <Grid size={12} container justifyContent="space-between" paddingRight={2}>
                                <Grid size={2}>
                                    <Typography fontSize="12px">{index + 1}.</Typography>
                                </Grid>
                                <Grid size={4}>
                                    <Typography fontSize="12px">
                                        <Tooltip
                                            enterDelay={300}
                                            title={dayjs(artifact.timestamp).tz(timezone).format(userValues.dateTimeFormat(user))}
                                            placement="bottom"
                                        >
                                            <span>
                                                {dayjs(artifact.timestamp)
                                                    .tz(timezone)
                                                    .format(
                                                        userValues.dateTimeFormat(user, {
                                                            exclude_hours: true,
                                                        }),
                                                    )}
                                            </span>
                                        </Tooltip>
                                    </Typography>
                                </Grid>
                                <Grid size={6}>
                                    <Typography fontSize="12px">
                                        {artifact.super_category ? artifact.super_category : "Unspecified Category"}
                                    </Typography>
                                </Grid>
                            </Grid>
                        </AccordionSummary>
                        <AccordionDetails sx={{ padding: 0, paddingTop: 1 }}>
                            {expanded === index && <Detection artifact={artifact} favouriteArtifacts={favouriteArtifacts} signedUrls={signedUrls} />}
                        </AccordionDetails>
                    </Accordion>
                </div>
            );
        };

        const handleLoadMore = async () => {
            setIsLoadingMore(true);
            setShowLoadMoreButton(false);
            try {
                const newArtifacts = await fetchMoreArtifacts();
                setIsLoadingMore(false);
                setLoadedArtifacts((prevArtifacts) => [...prevArtifacts, ...newArtifacts.artifacts]);
                setFavouriteArtifacts((prev) => [...prev, ...newArtifacts.favouritesArtifacts]);
            } catch (err) {
                console.error(err);
            }
        };

        if (!selectedStream || !selectedStream.StreamName) {
            return (
                <Typography color="primary.contrastText" paddingX={2} paddingY={1} width="100%" display="block">
                    No artifacts found
                </Typography>
            );
        }

        return (
            <Box
                className="dashboard-step-12"
                sx={{
                    minWidth: { xs: "0", md: "410px" },
                    width: "100%",
                }}
            >
                <Grid container alignItems={"center"} justifyContent={"space-between"} paddingRight={2}>
                    <Grid size="auto" gap={2}>
                        {isLoadingArtifacts ? (
                            <Grid item>
                                <Skeleton variant="rectangular" width={150} height={30} />
                            </Grid>
                        ) : artifacts?.length > 0 ? (
                            <Grid
                                alignItems={"center"}
                                sx={{
                                    position: "absolute",
                                    top: { xs: "30px", lg: "8px" },
                                    right: { xs: "15px", lg: "45px" },
                                }}
                            >
                                <Filters
                                    category={category}
                                    setCategory={setCategory}
                                    categories={categories}
                                    setStartTimestamp={setStartTimestamp}
                                    setEndTimestamp={setEndTimestamp}
                                />
                            </Grid>
                        ) : null}
                    </Grid>
                </Grid>
                {isLoadingArtifacts ? (
                    <Grid container gap={1} flexDirection="column">
                        {Array.from({ length: 3 }).map((_, i) => (
                            <Grid
                                key={i}
                                container
                                justifyContent="space-between"
                                alignItems="center"
                                bgcolor="primary.dark"
                                paddingY={2}
                                paddingX={2}
                            >
                                <Grid>
                                    <Skeleton variant="text" width={100} height={15} />
                                </Grid>
                                <Grid>
                                    <Skeleton variant="text" width={100} height={15} />
                                </Grid>
                            </Grid>
                        ))}
                    </Grid>
                ) : !isLoadingArtifacts && artifacts && artifacts.length === 0 ? (
                    <Typography color="primary.contrastText" variant="caption" paddingX={2} paddingY={1} width="100%" display="block">
                        No artifacts found for this stream
                    </Typography>
                ) : !isLoadingArtifacts && filteredArtifacts.length === 0 ? (
                    <Typography color="primary.contrastText" variant="caption" paddingX={2} paddingY={1} width="100%" display="block">
                        No artifacts to show
                    </Typography>
                ) : (
                    <>
                        <Grid container paddingY={2}>
                            <Grid fontWeight={500} fontSize={"14px"} sx={{ color: theme.palette.custom.mainBlue }} size={2}></Grid>
                            <Grid fontWeight={500} fontSize={"14px"} sx={{ color: theme.palette.custom.mainBlue }} size={3}>
                                Date/Time
                            </Grid>
                            <Grid fontWeight={500} fontSize={"14px"} sx={{ color: theme.palette.custom.mainBlue }} size={5.4}>
                                Category
                            </Grid>
                            <Grid fontWeight={500} fontSize={"14px"} sx={{ color: theme.palette.custom.mainBlue }}>
                                View
                            </Grid>
                        </Grid>
                        <List
                            ref={listRef}
                            height={deviceHeight > 820 ? 400 : 300}
                            itemCount={filteredArtifacts.length}
                            itemSize={getRowHeight}
                            width="100%"
                            onItemsRendered={handleItemsRendered}
                            onScroll={handleScroll}
                        >
                            {RenderRow}
                        </List>
                        <Box display="flex" justifyContent="center">
                            {isLoadingMore ? (
                                <Typography sx={{ color: "#FFFFFF" }}>Loading artifacts...</Typography>
                            ) : showLoadMoreButton && filteredArtifacts.length < 9 ? (
                                <Button
                                    onClick={handleLoadMore}
                                    sx={{
                                        padding: 0,
                                        color: "#FFFFFF",
                                        textDecoration: "underline",
                                    }}
                                >
                                    Load More
                                </Button>
                            ) : (
                                <></>
                            )}
                        </Box>
                    </>
                )}
            </Box>
        );
    },
);

Events.displayName = "Events";

export default Events;
