import "video.js/dist/video-js.css";
import { Grid, Tooltip, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { displayCoordinates, permissions } from "../../../utils";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller.js";
import { useUser } from "../../../hooks/UserHook.jsx";

const Detection = ({ artifact, favouriteArtifacts, signedUrls }) => {
    const [src, setSrc] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);
    const { user } = useUser();
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);
    const [flaggedArtifact, setFlaggedArtifact] = useState(false);
    // const [locationName, setLocationName] = useState('Loading...');
    const {
        location: { coordinates },
        video_path,
        others,
    } = artifact;
    const key = displayCoordinates([coordinates[0], coordinates[1]], !!user?.use_MGRS);
    // const location = { lat: coordinates[1], lng: coordinates[0] };

    // const fetchGeolocation = async () => {
    //     if (coordinates.length === 2) {
    //         try {
    //             const name = await getLocation(location);
    //             setLocationName(name);
    //         } catch (err) {
    //             console.error('Error fetching geolocation:', err);
    //         }
    //     }
    // };

    useEffect(() => {
        if (signedUrls && artifact) {
            const thumbnailUrl = signedUrls.get(`${artifact._id}:thumbnail`);
            const imageUrl = signedUrls.get(`${artifact._id}:image`);
            const videoUrl = signedUrls.get(`${artifact._id}:video`);

            if (artifact.video_path) {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(videoUrl || null);
            } else {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(imageUrl || null);
            }
        }
    }, [artifact, signedUrls]);

    useEffect(() => {
        setFlaggedArtifact(artifactFlagController.isArtifactFlaggedByUser(artifact._id));
    }, [artifactFlagController.userFlaggedArtifactIds.size]);

    // useEffect(() => {
    //     fetchGeolocation();
    // }, []);

    return (
        <Grid container color="#FFFFFF" flexDirection={{ xs: "column", sm: "row" }} gap={1} sx={{ backgroundColor: "primary.main" }} padding={1}>
            <Grid position="relative" size="grow">
                <PreviewMedia
                    thumbnailLink={thumbnail} // here will be thumbnail
                    originalLink={src} // put here link on original image
                    showVideoModal={true}
                    cardId={artifact._id}
                    favouriteArtifacts={favouriteArtifacts}
                    isImage={!video_path}
                    style={{ borderRadius: 8 }}
                    skeletonStyle={{ height: "100%", borderRadius: "8px" }}
                    showFullscreenIcon={true}
                    showArchiveButton={hasManageArtifacts}
                    isArchived={artifact.is_archived}
                    vesselId={artifact?.onboard_vessel_id}
                    direction="row-reverse"
                    flaggedArtifact={flaggedArtifact}
                />
            </Grid>
            <Grid container flexDirection="column" gap={1} size="grow">
                <Grid fontWeight={500} fontSize="14px" sx={{ color: theme.palette.custom.mainBlue }}>
                    Location
                </Grid>
                <Grid fontWeight={500} fontSize="14px">
                    {/* <Tooltip enterDelay={300} title={key} placement="bottom"> */}
                    <Typography fontSize="14px" fontWeight={400}>
                        {key}
                    </Typography>
                    {/* </Tooltip> */}
                </Grid>
                <Grid fontWeight={500} fontSize="14px" sx={{ color: theme.palette.custom.mainBlue }}>
                    Description
                </Grid>
                <Grid fontWeight={500} fontSize="14px">
                    <Tooltip enterDelay={300} title={others?.length > 45 ? others : ""} placement="bottom">
                        <span>{others ? (others.length > 45 ? `${others.slice(0, 45)}...` : others) : "--"}</span>
                    </Tooltip>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default Detection;
