import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { GoogleMap } from "@react-google-maps/api";
import { Skeleton } from "@mui/material";
import dayjs from "dayjs";
import "video.js/dist/video-js.css";
import { useLocation } from "react-router-dom";
import { useUser } from "../../../hooks/UserHook";
import idb from "../../../indexedDB";
import axiosInstance from "../../../axios";
import { useToaster } from "../../../hooks/ToasterHook";
import { useApp } from "../../../hooks/AppHook";
import { defaultValues } from "../../../utils";
import { drawArtifacts, drawCoordinates, storeArtifacts, filterArtifactsNearHomePorts } from "./Map";
import ShareModal from "../../../components/ShareModal";
import s3Controller from "../../../controllers/S3.controller";
import FullscreenMediaModal from "../../../components/FullscreenMediaModal";
import { logEvent } from "../../../utils";
import useVesselInfo from "../../../hooks/VesselInfoHook";
import environment from "../../../../environment";
import { getSocket } from "../../../socket";

const MainMap = ({
    vessels = [],
    loadingVessels,
    setLoadingVessels,
    setErrorsomeVessels,
    errorsomeVessels,
    setAvailableVessels,
    setEmptyVessels,
    emptyVessels,
    focusedVessel,
    datapointsDistance = defaultValues.datapointsDistance,
    journeyStart = defaultValues.journeyStart,
    journeyEnd = defaultValues.journeyEnd,
    initialZoom = defaultValues.zoom,
    interval = defaultValues.interval,
    precision = defaultValues.precision,
    setArtifactsCategories,
    selectedArtifactsCategory,
    timeSlider,
    showDatapoints,
    showArtifacts,
    homePortsArtifactsMode,
    selectedNumberOfArtifacts,
    artifactsType,
    showFilteredCoordinates,
    // fromDate,
    // toDate,
    // showCoordinatesPoints,
    setSelectedArtifactsCategory,
}) => {
    const defaultCenter = { lat: 12.203715897544681, lng: 122.97040789336037 };
    const { google, timezone } = useApp();
    const { vesselInfo } = useVesselInfo();
    const toaster = useToaster();
    const { pathname } = useLocation();
    const [map, setMap] = useState(null);
    const [zoom, setZoom] = useState(initialZoom);
    const [loadingCoordinates, setLoadingCoordinates] = useState(true);
    const [coordinates, setCoordinates] = useState({});
    const [filteredCoordinates, setFilteredCoordinates] = useState({});
    const [dataSetLayers, setDataSetLayers] = useState({});
    // const [unfilteredDataLayer, setUnfilteredDataLayer] = useState({});
    const [flightPaths, setFlightPaths] = useState({});

    const clustererRef = useRef(null);
    const loadingVesselsRef = useRef([]);

    // eslint-disable-next-line no-unused-vars
    const [loadingArtifacts, setLoadingArtifacts] = useState(true);
    const [artifacts, setArtifacts] = useState({});
    const [artifactsMarkers, setArtifactsMarkers] = useState({});
    const [filteredArtifacts, setFilteredArtifacts] = useState({});
    const [signedUrls, setSignedUrls] = useState(new Map());
    const [signedUrlsVersion, setSignedUrlsVersion] = useState(0);

    const infoWindow = useMemo(() => google && new google.maps.InfoWindow({ disableAutoPan: true }), [google]);
    const artifactInfowWindow = useMemo(() => google && new google.maps.InfoWindow({ disableAutoPan: true }), [google]);
    let currentClusterInfoWindow = useMemo(() => google && new google.maps.InfoWindow({ disableAutoPan: true }), [google]);
    const center = useMemo(() => {
        const centerVessel = filteredCoordinates[focusedVessel];
        return centerVessel?.[centerVessel.length - 1];
    }, [focusedVessel]);

    const prevFilteredCoordinates = useRef({});
    const prevFilteredArtifacts = useRef({});

    const cachedSrc = useRef({});
    const [favouriteArtifacts, setFavouriteArtifacts] = useState([]);
    const favouriteArtifactsRef = useRef(favouriteArtifacts);
    const { user } = useUser();
    const [isShareModalOpen, setIsShareModalOpen] = useState(false);
    const [artifactToShare, setArtifactToShare] = useState(null);
    const [isFullscreenOpen, setIsFullscreenOpen] = useState(false);
    const [artifactToFullscreen, setArtifactToFullscreen] = useState(null);

    useEffect(() => {
        favouriteArtifactsRef.current = favouriteArtifacts;
        // console.log("updated ref state");
    }, [favouriteArtifacts]);

    // Log when share or fullscreen modals are opened for an artifact
    useEffect(() => {
        if (isShareModalOpen && artifactToShare && artifactToShare._id) {
            logEvent("ArtifactShareOpened", { artifactId: artifactToShare._id });
        }
    }, [isShareModalOpen, artifactToShare]);

    useEffect(() => {
        if (isFullscreenOpen && artifactToFullscreen && artifactToFullscreen._id) {
            logEvent("ArtifactFullscreenOpened", { artifactId: artifactToFullscreen._id });
        }
    }, [isFullscreenOpen, artifactToFullscreen]);

    const storePrevFilteredArtifacts = useCallback(
        (filteredArtifacts) => {
            if (!map) return;
            prevFilteredArtifacts.current = { ...filteredArtifacts };
        },
        [map],
    );

    //artifact range setting and loading
    useEffect(() => {
        storePrevFilteredArtifacts(filteredArtifacts);
        const applyFilters = async () => {
            const computedArtifacts = { ...artifacts };
            Object.keys(computedArtifacts).forEach((key) => {
                computedArtifacts[key] = artifacts[key]
                    .filter(
                        (a) =>
                            dayjs(a.timestamp).valueOf() >= journeyStart.valueOf() &&
                            dayjs(a.timestamp).valueOf() <= (journeyEnd === "now" ? dayjs().valueOf() : journeyEnd.valueOf()),
                    ) // filter for time range
                    .filter((a) => {
                        if (!selectedArtifactsCategory || selectedArtifactsCategory.length === 0) {
                            return false;
                        }
                        return selectedArtifactsCategory.includes(a.super_category);
                    }) // filter for category
                    .filter((a) => {
                        if (artifactsType === "video") {
                            return a.video_path;
                        } else if (artifactsType === "image") {
                            return !a.video_path;
                        }
                        return true;
                    }); //filter for artifact type
                if (computedArtifacts[key].length >= selectedNumberOfArtifacts && selectedNumberOfArtifacts !== "all") {
                    computedArtifacts[key] = computedArtifacts[key].slice(0, selectedNumberOfArtifacts - 1);
                    console.warn(
                        "Category",
                        selectedArtifactsCategory,
                        "has a lot of artifacts. Only showing",
                        selectedNumberOfArtifacts,
                        "to conserve resources for type",
                        artifactsType,
                    );
                }
            });

            const homePortsFilteredArtifacts = await filterArtifactsNearHomePorts(computedArtifacts, homePortsArtifactsMode);

            setFilteredArtifacts(homePortsFilteredArtifacts);
        };

        applyFilters();
    }, [artifacts, selectedArtifactsCategory, selectedNumberOfArtifacts, artifactsType, journeyStart, journeyEnd, homePortsArtifactsMode]);

    const storePrevCoordinates = useCallback(
        (filteredCoordinates) => {
            if (!map) return;
            prevFilteredCoordinates.current = { ...filteredCoordinates };
        },
        [map],
    );

    // useEffect(() => {
    //     const ts = Date.now();
    //     console.log("[computing coordinates] executing...");
    //     storePrevCoordinates(filteredCoordinates);
    //     const computedCoordinates = { ...coordinates };
    //     console.log(
    //         "[computing coorindates] length",
    //         Object.keys(coordinates).reduce((acc, key) => acc + coordinates[key].length, 0),
    //     );
    //     const journeyStartValue = dayjs(journeyStart).valueOf();
    //     const journeyEndValue = journeyEnd === "now" ? dayjs().valueOf() : dayjs(journeyEnd).valueOf();
    //     Object.keys(computedCoordinates).forEach((key) => {
    //         computedCoordinates[key] = coordinates[key]
    //             .filter((c) => {
    //                 const currTimestamp = new Date(c.timestamp).getTime();
    //                 return currTimestamp >= journeyStartValue && currTimestamp <= journeyEndValue;
    //             }) // filter for time range
    //             // .filter((_, i, self) => i % interval === 0 || i === self.length - 1) // filter for interval
    //             .map((c) => ({ ...c, lat: parseFloat(c.lat.toFixed(precision)), lng: parseFloat(c.lng.toFixed(precision)) })); // map for location precision
    //         // .filter(
    //         //     (value, index, self) => index === self.length - 1 || self.findIndex((v) => v.lat === value.lat && v.lng === value.lng) === index,
    //         // ); // remove unnecessary duplicated coordinates

    //         // Compute rotation
    //         const headingTs = Date.now();
    //         for (let i = 0; i < computedCoordinates[key].length; i++) {
    //             if (i < computedCoordinates[key].length - 1) {
    //                 computedCoordinates[key][i].rotation = google.maps.geometry.spherical.computeHeading(
    //                     new google.maps.LatLng(computedCoordinates[key][i].lat, computedCoordinates[key][i].lng),
    //                     new google.maps.LatLng(computedCoordinates[key][i + 1].lat, computedCoordinates[key][i + 1].lng)
    //                 );
    //             } else {
    //                 computedCoordinates[key][i].rotation = 0; // Last point
    //             }
    //         }
    //         console.log(
    //             `[computing coordinates] computed rotation for ${key} in`,
    //             Date.now() - headingTs,
    //             "ms"
    //         );
    //     });
    //     setFilteredCoordinates(computedCoordinates);
    //     console.log("[computing coordinates] done in", Date.now() - ts, "ms");
    // }, [coordinates, journeyStart, journeyEnd, precision]);

    useEffect(() => {
        const ts = Date.now();
        console.log("[computing coordinates] executing...");
        storePrevCoordinates(filteredCoordinates);
        const computedCoordinates = {};
        console.log(
            "[computing coorindates] length",
            Object.keys(coordinates).reduce((acc, key) => acc + coordinates[key].length, 0),
        );
        const journeyStartValue = dayjs(journeyStart).valueOf();
        const journeyEndValue = journeyEnd === "now" ? dayjs().valueOf() : dayjs(journeyEnd).valueOf();

        Object.keys(coordinates).forEach((key) => {
            const filteredList = [];
            const originalList = coordinates[key];

            let prev = null;

            for (const point of originalList) {
                const currTimestamp = new Date(point.timestamp).getTime();
                if (currTimestamp < journeyStartValue || currTimestamp > journeyEndValue) continue;
                const lat = parseFloat(point.lat.toFixed(precision));
                const lng = parseFloat(point.lng.toFixed(precision));
                const current = { ...point, lat, lng };
                if (prev) {
                    prev.rotation = google.maps.geometry.spherical.computeHeading(
                        new google.maps.LatLng(prev.lat, prev.lng),
                        new google.maps.LatLng(current.lat, current.lng),
                    );
                    filteredList.push(prev);
                }
                // Set current as next previous
                prev = current;
            }
            // Handle last point (no rotation)
            if (prev) {
                prev.rotation = 0;
                filteredList.push(prev);
            }
            computedCoordinates[key] = filteredList;
            console.log(`[computing coordinates] processed ${filteredList.length} points for ${key}`);
        });
        setFilteredCoordinates(computedCoordinates);
        console.log("[computing coordinates] done in", Date.now() - ts, "ms");
    }, [coordinates, journeyStart, journeyEnd, precision]);

    const vesselsRef = useRef(vessels);
    useEffect(() => {
        const skipVesselsCoordinates = vessels
            .filter((v) => vesselsRef.current.find((_v) => _v.id === v.id) && Object.keys(coordinates).includes(v.id))
            .map((v) => v.id);
        const skipVesselsArtifacts = vessels
            .filter((v) => vesselsRef.current.find((_v) => _v.id === v.id) && Object.keys(artifacts).includes(v.id))
            .map((v) => v.id);

        if (vessels.length === 0) {
            setCoordinates({});
            setArtifacts({});
            return;
        }

        if (Object.keys(coordinates).some((vesselName) => !vessels.find((v) => v.id === vesselName))) {
            setCoordinates((v) => {
                const newCoordinates = {};
                Object.keys(coordinates)
                    .filter((vesselName) => vessels.find((v) => v.id === vesselName))
                    .forEach((vesselName) => {
                        newCoordinates[vesselName] = v[vesselName];
                    });
                return newCoordinates;
            });
        }
        if (Object.keys(artifacts).some((vesselName) => !vessels.find((v) => v.id === vesselName))) {
            setArtifacts((v) => {
                const newArtifacts = {};
                Object.keys(artifacts)
                    .filter((vesselName) => vessels.find((v) => v.id === vesselName))
                    .forEach((vesselName) => {
                        newArtifacts[vesselName] = v[vesselName];
                    });
                return newArtifacts;
            });
        }

        vesselsRef.current = vessels;

        console.log("vesselsRef.current", vesselsRef.current);

        fetchCoordinates({ skipVessels: skipVesselsCoordinates });
        fetchArtifacts({ skipVessels: skipVesselsArtifacts });
    }, [vessels]);

    useEffect(() => {
        fetchCoordinates();
        fetchArtifacts();
    }, [timeSlider]);

    const autoRefreshIntervalRef = useRef(null);
    // auto refreh every 5m
    useEffect(() => {
        if (journeyEnd === "now") {
            if (autoRefreshIntervalRef.current) {
                clearInterval(autoRefreshIntervalRef.current);
            }
            autoRefreshIntervalRef.current = setInterval(
                () => {
                    console.info("refreshing map data");
                    fetchCoordinates();
                    fetchArtifacts();
                },
                5 * 60 * 1000,
            );
        } else {
            clearInterval(autoRefreshIntervalRef.current);
        }
        return () => clearInterval(autoRefreshIntervalRef.current);
    }, [timeSlider, journeyEnd, coordinates]);

    useEffect(() => {
        const fetchFilters = async () => {
            try {
                const filterItems = await axiosInstance
                    .get("/artifacts/filters")
                    .then((res) => res.data)
                    .catch((err) => {
                        console.error(`Error fetching filters in Events`, err);
                    });
                if (Object.keys(filterItems).length !== 0) {
                    const categories = filterItems["superCategories"] || [];
                    setArtifactsCategories(categories);
                    setSelectedArtifactsCategory(categories);
                }
            } catch (err) {
                console.error(`Error fetching filters in Events`, err);
            }
        };
        fetchFilters();

        const socket = getSocket();
        const handleArtifactChanged = (data) => {
            const updatedArtifact = data?.artifact;
            if (!updatedArtifact) return;
            updatedArtifact.lat = updatedArtifact.location.coordinates[1];
            updatedArtifact.lng = updatedArtifact.location.coordinates[0];
            setArtifacts((prev) => {
                if (!updatedArtifact.onboard_vessel_id) return prev;
                const newArtifacts = { ...prev };
                const vesselId = updatedArtifact.onboard_vessel_id;
                const vesselArtifacts = newArtifacts[vesselId] ? [...newArtifacts[vesselId]] : [];

                if (updatedArtifact.is_archived) {
                    newArtifacts[vesselId] = vesselArtifacts.filter((a) => a._id !== updatedArtifact._id);
                } else {
                    const idx = vesselArtifacts.findIndex((a) => a._id === updatedArtifact._id);
                    if (idx !== -1) {
                        vesselArtifacts[idx] = updatedArtifact;
                    } else {
                        vesselArtifacts.push(updatedArtifact);
                    }
                    newArtifacts[vesselId] = vesselArtifacts;
                }

                return newArtifacts;
            });
        };
        socket.on("artifact/changed", handleArtifactChanged);
        return () => socket.off("artifact/changed", handleArtifactChanged);
    }, []);

    /** This has been removed because this leads to unnecessary rerender */
    // useEffect(() => {
    //     const vesselIds = Object.keys(coordinates);

    //     const addCoordinate = (data) => {
    //         console.log('[addCoordinate] data', data);
    //         setCoordinates((v) => {
    //             if (!v[data.vesselName]) {
    //                 return v;
    //             }
    //             const new_coordinates = { ...v };
    //             new_coordinates[data.vesselName] = new_coordinates[data.vesselName].concat({ ...data, lat: data.latitude, lng: data.longitude });
    //             return new_coordinates;
    //         });
    //     };

    //     vesselIds.forEach((id) => {
    //         gps_socket.on(`${id}_location/insert`, addCoordinate);
    //     });

    //     return () => {
    //         vesselIds.forEach((id) => {
    //             gps_socket.off(`${id}_location/insert`, addCoordinate);
    //         });
    //     };
    // }, [coordinates]);

    useEffect(() => {
        prevFilteredCoordinates.current = {};
    }, [datapointsDistance, showDatapoints, showFilteredCoordinates]);

    useEffect(() => {
        if (!map) return;
        console.log("calling drawCoordinates");
        drawCoordinates(
            user,
            filteredCoordinates,
            prevFilteredCoordinates,
            flightPaths,
            setFlightPaths,
            dataSetLayers,
            setDataSetLayers,
            vessels,
            coordinates,
            journeyStart,
            journeyEnd,
            map,
            infoWindow,
            datapointsDistance,
            showDatapoints,
            google,
            timezone,
            showFilteredCoordinates,
            // fromDate,
            // toDate,
            // unfilteredDataLayer,
            // setUnfilteredDataLayer,
            // showCoordinatesPoints,
            // devMode,
            interval,
            vesselInfo,
        );
    }, [
        map,
        coordinates,
        filteredCoordinates,
        datapointsDistance,
        showDatapoints,
        google,
        showFilteredCoordinates,
        // fromDate,
        // toDate,
        // showCoordinatesPoints,
        interval,
    ]);

    useEffect(() => {
        if (!map) return;
        console.log("calling drawArtifacts");
        drawArtifacts(
            showArtifacts,
            artifactsMarkers,
            google,
            clustererRef,
            map,
            filteredArtifacts,
            prevFilteredArtifacts,
            artifactInfowWindow,
            setArtifactsMarkers,
            currentClusterInfoWindow,
            favouriteArtifactsRef,
            user,
            timezone,
            vesselInfo,
            setFavouriteArtifacts,
            cachedSrc,
            setArtifactToShare,
            setIsShareModalOpen,
            setArtifactToFullscreen,
            setIsFullscreenOpen,
            signedUrls,
        );
    }, [map, filteredArtifacts, showArtifacts, signedUrlsVersion]);

    useEffect(() => {
        setAvailableVessels(vessels.filter((v) => filteredCoordinates[v.id] && filteredCoordinates[v.id].length > 0));
        setEmptyVessels(vessels.filter((v) => filteredCoordinates[v.id] && filteredCoordinates[v.id].length === 0));
        if (loadingCoordinates) return;
        if (vessels.length === 0 && pathname.includes("/map")) {
            toaster("No vessel selected", { variant: "warning" });
        }
    }, [vessels, filteredCoordinates]);

    useEffect(() => {
        const errorEmptyVessels = [...errorsomeVessels, ...emptyVessels];
        const isErrorOrEmpty = errorEmptyVessels.some((vessel) => vessel.id !== focusedVessel);
        if (loadingCoordinates) return;
        if (
            loadingVessels.length == 0 &&
            isErrorOrEmpty &&
            Object.values(filteredCoordinates).every((v) => v.length === 0) &&
            pathname.includes("/map")
        ) {
            toaster("No coordinates found for the selected vessels", { variant: "warning" });
        }
    }, [loadingVessels, errorsomeVessels, emptyVessels]);

    useEffect(() => {
        Object.keys(dataSetLayers).forEach((vesselName) => {
            const dataLayer = dataSetLayers[vesselName];
            if (!dataLayer) return;

            let lastFeature = null;
            dataLayer.forEach((feature) => {
                lastFeature = feature;
            });

            if (!lastFeature) return;

            const color = focusedVessel === vesselName ? "#ff0000" : defaultValues.polylineColors[vesselName] || "#0000FF";

            dataLayer.overrideStyle(lastFeature, {
                icons: [
                    {
                        icon: {
                            path: defaultValues.icons.location,
                            strokeColor: "#000000",
                            strokeWeight: 0.5,
                            scale: 1.2,
                            fillColor: color,
                            fillOpacity: 1,
                            anchor: new google.maps.Point(10, 20),
                        },
                    },
                ],
            });
        });
    }, [focusedVessel, dataSetLayers]);

    const coordinatesCurrentlyLoading = useRef(new Map());
    const fetchCoordinates = ({ skipVessels = [] } = {}) => {
        Promise.allSettled(
            vesselsRef.current
                .filter((v) => !skipVessels.includes(v.id) && !coordinatesCurrentlyLoading.current.has(v.id))
                .map(
                    (vessel) =>
                        new Promise((resolve, reject) => {
                            // const ts = Date.now();
                            // const store = vessel.id + "_location";
                            // const filter = (item) => dayjs(item.timestamp).isBetween(timeSlider[0], timeSlider[1], null, "[]");
                            // idb.getItems(store, filter)
                            //     .then((storedCoordinates) => {
                            //     })
                            //     .catch((err) => {
                            //         console.error(err);
                            //         reject({
                            //             vessel,
                            //             err,
                            //         });
                            //     });
                            // if (skipVessels.includes(vessel.id)) {
                            //     return resolve({
                            //         vessel,
                            //         coordinates: coordinates[vessel.id],
                            //     });
                            // }
                            setLoadingVessels((v) => {
                                loadingVesselsRef.current = v.concat(vessel.id);
                                return loadingVesselsRef.current;
                            });
                            // const filteredStoredCoordinates = storedCoordinates.filter((item) =>
                            //     dayjs(item.timestamp).isBetween(timeSlider[0], timeSlider[1], null, "[]"),
                            // );
                            // console.log(vessel.id, 'time taken to get stored coordinates', Date.now() - ts, 'ms');
                            const payload = {
                                startTimestamp: timeSlider[0],
                                endTimestamp: journeyEnd === "now" ? dayjs().valueOf() : timeSlider[1],
                                // excludeIds: filteredStoredCoordinates.map((item) => item._id),
                            };
                            coordinatesCurrentlyLoading.current.set(vessel.id, {
                                vessel_id: vessel.vessel_id,
                                payload,
                            });
                            axiosInstance
                                .post(`/v2/vesselLocations/${vessel.vessel_id}`, payload, { meta: { showSnackbar: false } })
                                .then((res) => {
                                    // if (res.data.length > 0) storeCoordinates(vessel.id, res.data);
                                    const coordinates = res.data
                                        // .concat(storedCoordinates)
                                        .map((c) => ({
                                            ...c,
                                            lat: c.latitude,
                                            lng: c.longitude,
                                        }))
                                        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
                                    // Update vessel availability immediately
                                    if (coordinates.length > 0) {
                                        setAvailableVessels((prev) => {
                                            if (prev.some((v) => v.id === vessel.id)) return prev;
                                            return [...prev, vessel];
                                        });
                                        setEmptyVessels((prev) => prev.filter((v) => v.id !== vessel.id));
                                    } else {
                                        setEmptyVessels((prev) => {
                                            if (prev.some((v) => v.id === vessel.id)) return prev;
                                            return [...prev, vessel];
                                        });
                                        setAvailableVessels((prev) => prev.filter((v) => v.id !== vessel.id));
                                    }
                                    // Set coordinates only if vessel is still in the ref list
                                    if (vesselsRef.current.find((v) => v.id === vessel.id)) {
                                        setCoordinates((prev) => ({ ...prev, [vessel.id]: coordinates }));
                                    }
                                    resolve();
                                })
                                .catch((err) => {
                                    console.error("Error fetching coordinates:", err);
                                    // Track error vessels here (replacing outer .then)
                                    setErrorsomeVessels((prev) => {
                                        if (prev.some((v) => v.id === vessel.id)) return prev;
                                        return [...prev, vessel];
                                    });
                                    reject(err);
                                })
                                .finally(() => {
                                    coordinatesCurrentlyLoading.current.delete(vessel.id);
                                    setLoadingVessels((v) => {
                                        loadingVesselsRef.current = v.filter((id) => id !== vessel.id);
                                        return loadingVesselsRef.current;
                                    });
                                });
                        }),
                ),
        )
            .catch((err) => {
                console.error("Unexpected error in fetchCoordinates:", err);
            })
            .finally(() => setLoadingCoordinates(false));
    };

    const artifactsCurrentlyLoading = useRef(new Map());
    const fetchArtifacts = ({ skipVessels = [] } = {}) => {
        console.log("fetching artifacts", vesselsRef.current);
        // skipVessels = []
        // clearTimeout(artifactsTimeout.current);
        // artifactsTimeout.current = setTimeout(() => {
        Promise.all(
            vesselsRef.current
                .filter((v) => !skipVessels.includes(v.id) && !artifactsCurrentlyLoading.current.has(v.id))
                .map(
                    (vessel) =>
                        new Promise((resolve, reject) => {
                            const store = vessel.id + "_artifact";
                            idb.getItems(store)
                                .then((storedArtifacts) => {
                                    const filteredStoredArtifacts = storedArtifacts.filter((item) =>
                                        dayjs(item.timestamp).isBetween(timeSlider[0], timeSlider[1], null, "[]"),
                                    );
                                    const payload = {
                                        startTimestamp: timeSlider[0],
                                        endTimestamp: journeyEnd === "now" ? dayjs().valueOf() : timeSlider[1],
                                        excludeIds: filteredStoredArtifacts.map((item) => item._id),
                                        favourites: 1,
                                    };
                                    artifactsCurrentlyLoading.current.set(vessel.id, {
                                        vessel_id: vessel.vessel_id,
                                        payload,
                                    });
                                    axiosInstance
                                        .post("/v2/artifacts/" + vessel.vessel_id, payload, { meta: { showSnackbar: false } })
                                        .then((res) => {
                                            // console.log("artifact data in map", res.data, 'favourites', res.data.favouritesArtifacts)
                                            if (res.data.artifacts.length > 0) {
                                                storeArtifacts(vessel.id, res.data.artifacts);
                                                setFavouriteArtifacts(res.data.favouritesArtifacts);
                                                // console.log("coming here to update the ref", res.data.favouriteArtifacts, "favourtieArtifacts", favouriteArtifacts)
                                                favouriteArtifactsRef.current = res.data.favouritesArtifacts;
                                            }
                                            const artifacts = res.data.artifacts.concat(storedArtifacts).map((a) => ({
                                                ...a,
                                                lat: a.location.coordinates[1],
                                                lng: a.location.coordinates[0],
                                            }));
                                            setFavouriteArtifacts(res.data.favouritesArtifacts);
                                            resolve({
                                                vessel,
                                                artifacts,
                                            });
                                        })
                                        .catch((err) => {
                                            console.error(err);
                                            reject({
                                                vessel,
                                                err,
                                            });
                                        })
                                        .finally(() => {
                                            artifactsCurrentlyLoading.current.delete(vessel.id);
                                        });
                                })
                                .catch((err) => {
                                    console.error(err);
                                    reject({
                                        vessel,
                                        err,
                                    });
                                });
                        }),
                ),
        )
            .then(async (ress) => {
                if (ress.length === 0) return;

                const allArtifacts = [];
                const newArtifacts = {};

                ress.filter((res) => vesselsRef.current.some((v) => v.id === res.vessel.id)).forEach((res) => {
                    newArtifacts[res.vessel.id] = res.artifacts;
                    allArtifacts.push(...res.artifacts);
                });

                setArtifacts((prev) => ({ ...prev, ...newArtifacts }));

                if (allArtifacts.length > 0) {
                    try {
                        const newSignedUrls = await s3Controller.fetchSignedUrlsBatch(allArtifacts);
                        setSignedUrls((prevUrls) => {
                            const mergedUrls = new Map(prevUrls);
                            newSignedUrls.forEach((value, key) => {
                                mergedUrls.set(key, value);
                            });
                            return mergedUrls;
                        });
                        setSignedUrlsVersion((prev) => prev + 1);
                    } catch (error) {
                        console.error("[MainMap] Error fetching signed URLs batch:", error);
                    }
                }
            })
            .catch(console.error)
            .finally(() => setLoadingArtifacts(false));
        // }, 500);
    };
    const onLoad = (mapInstance) => {
        setMap(mapInstance);
    };

    const onUnmount = () => {
        setMap(null);
        prevFilteredCoordinates.current = {};
    };

    const toggleShare = () => {
        setIsShareModalOpen(false);
        setArtifactToShare(null);
    };

    useEffect(() => {
        if (!pathname.includes("/map") && currentClusterInfoWindow) {
            currentClusterInfoWindow.close();
        }
        if (!pathname.includes("/map") && artifactInfowWindow) {
            artifactInfowWindow.close();
        }
    }, [pathname]);

    return (
        <div style={{ color: "#FFFFFF", width: "100%", height: "100%" }}>
            {loadingCoordinates ? <Skeleton animation="wave" variant="rectangular" height={"100%"} /> : <></>}
            <div style={{ display: !google || loadingCoordinates ? "none" : "flex", width: "100%", height: "100%" }}>
                {google && (
                    <GoogleMap
                        mapContainerStyle={{
                            width: "100%",
                            height: "100%",
                        }}
                        center={center || defaultCenter}
                        zoom={zoom}
                        onZoomChanged={() => {
                            if (map) {
                                setZoom(map.getZoom());
                            }
                        }}
                        onLoad={onLoad}
                        onUnmount={onUnmount}
                        options={{
                            zoomControl: true,
                            streetViewControl: true,
                            mapTypeControl: false,
                            fullscreenControl: false,
                            styles: [
                                {
                                    featureType: "landscape",
                                    stylers: [{ visibility: "off" }],
                                },
                                {
                                    featureType: "poi",
                                    stylers: [{ visibility: "off" }],
                                },
                                {
                                    featureType: "road",
                                    stylers: [{ visibility: "off" }],
                                },
                                {
                                    featureType: "transit",
                                    stylers: [{ visibility: "off" }],
                                },
                                {
                                    featureType: "water",
                                    stylers: [{ visibility: "simplified" }],
                                },
                            ],
                        }}
                    />
                )}
            </div>
            <ShareModal
                state={isShareModalOpen}
                onClose={toggleShare}
                shareLink={artifactToShare ? s3Controller.fetchUrl(artifactToShare, artifactToShare?.video_path ? "video" : "image") : ""}
                isImageLink={!artifactToShare?.video_path}
                shareEventLink={artifactToShare?._id ? `${environment.VITE_API_URL}/dashboard/events/${artifactToShare?._id}` : ""}
            />
            <FullscreenMediaModal
                open={isFullscreenOpen}
                onClose={() => {
                    setIsFullscreenOpen(false);
                    setArtifactToFullscreen(null);
                }}
                mediaUrl={
                    artifactToFullscreen ? s3Controller.fetchUrl(artifactToFullscreen, artifactToFullscreen.video_path ? "video" : "image") : ""
                }
                isImage={!artifactToFullscreen?.video_path}
            />
        </div>
    );
};

export default MainMap;
