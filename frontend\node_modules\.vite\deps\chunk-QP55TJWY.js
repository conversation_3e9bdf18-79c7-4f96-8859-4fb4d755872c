import {
  createSvgIcon
} from "./chunk-WC2UBJJB.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/icons-material/esm/FiberManualRecord.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var FiberManualRecord_default = createSvgIcon((0, import_jsx_runtime.jsx)("circle", {
  cx: "12",
  cy: "12",
  r: "8"
}), "FiberManualRecord");

export {
  FiberManualRecord_default
};
//# sourceMappingURL=chunk-QP55TJWY.js.map
