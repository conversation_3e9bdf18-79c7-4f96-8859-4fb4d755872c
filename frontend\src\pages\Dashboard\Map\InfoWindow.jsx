import React, { useEffect, useMemo, useState } from "react";
import { Grid, Typography, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import PreviewMedia from "../../../components/PreviewMedia";
import dayjs from "dayjs";
import { defaultValues, handleVesselTimezone, simplifyTimezone, permissions, displayCoordinates } from "../../../utils";
import favouriteArtifactsController from "../../../controllers/FavouriteArtifacts.controller";
import { UserProvider } from "../../../providers/UserProvider";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";
import { getSocket } from "../../../socket";

const InfoWindow = ({ artifact, artifactInfowWindow, vesselInfo, user, signedUrls }) => {
    const [src, setSrc] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);
    const [favouriteArtifacts, setFavouriteArtifacts] = useState([]);
    const [flaggedArtifact, setFlaggedArtifact] = useState(false);
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);
    const vesselTimezone = useMemo(() => {
        const timezone = handleVesselTimezone(artifact, vesselInfo);
        return timezone;
    }, [artifact, vesselInfo]);

    const fetchFavouriteArtifacts = async () => {
        const { favourites } = await favouriteArtifactsController.getUserFavouriteArtifacts();
        setFavouriteArtifacts(favourites);
    };

    const handleFlagChanged = async () => {
        await artifactFlagController.getUserFlaggedArtifactIds();
        setFlaggedArtifact(artifactFlagController.isArtifactFlaggedByUser(artifact._id));
    };

    useEffect(() => {
        const socket = getSocket();

        fetchFavouriteArtifacts();
        handleFlagChanged();

        socket.on("artifacts_flagged/changed", handleFlagChanged);
        return () => socket.off("artifacts_flagged/changed", handleFlagChanged);
    }, []);

    useEffect(() => {
        if (artifact && signedUrls && signedUrls instanceof Map && signedUrls.size > 0) {
            const thumbnailUrl = signedUrls.get(`${artifact._id}:thumbnail`);
            const imageUrl = signedUrls.get(`${artifact._id}:image`);
            const videoUrl = signedUrls.get(`${artifact._id}:video`);

            if (artifact.video_path) {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(videoUrl || null);
            } else {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(imageUrl || null);
            }
        }
    }, [artifact, signedUrls]);

    const handleClose = () => {
        artifactInfowWindow.close();
    };

    return (
        <Grid
            container
            direction="column"
            sx={{
                padding: 2,
                backgroundColor: "#343B44",
                color: "white",
                borderRadius: 2,
                maxWidth: 330,
                gap: 2,
            }}
        >
            {/* Add custom styles for InfoWindow */}
            <style>
                {`
                    .gm-style-iw-chr, .gm-style-iw-tc {
                        display: none !important;
                    }
                    .gm-style .gm-style-iw-c {
                        background-color: #343B44 !important;
                        outline: none;
                        padding: 0;
                    }
                    .gm-style .gm-style-iw-d {
                        overflow: auto !important;
                    }
                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {
                        background-color: #fff !important;
                    }
                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {
                        background: #343B44 !important;
                    }
                `}
            </style>
            {/* Header */}
            <Grid container justifyContent="space-between" alignItems="center">
                <Typography variant="h6">{artifact.name || "Artifact"}</Typography>
                <IconButton
                    onClick={handleClose}
                    sx={{
                        color: "white",
                        border: "1px solid white",
                        "&:hover": {
                            backgroundColor: "white",
                            color: "#4F5968",
                        },
                    }}
                >
                    <CloseIcon sx={{ fontSize: "14px" }} />
                </IconButton>
            </Grid>
            <Grid
                sx={{
                    position: "relative",
                    backgroundColor: "#343B44",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    height: 200,
                    borderRadius: 1,
                }}
            >
                <PreviewMedia
                    thumbnailLink={thumbnail}
                    originalLink={src}
                    cardId={artifact._id}
                    isImage={!artifact.video_path}
                    style={{ borderRadius: 8 }}
                    favouriteArtifacts={favouriteArtifacts}
                    showFullscreenIconForMap={!artifact.video_path}
                    showVideoThumbnail={artifact.video_path}
                    showArchiveButton={hasManageArtifacts}
                    isArchived={artifact.is_archived}
                    vesselId={artifact?.onboard_vessel_id}
                    // userTest={user}
                    skeletonStyle={{
                        minHeight: 200,
                        minWidth: 290,
                    }}
                    flaggedArtifact={flaggedArtifact}
                />
            </Grid>
            <Grid>
                <Typography>
                    <strong>Super Category:</strong> {artifact.super_category || "Not available"}
                </Typography>
                <Typography>
                    <strong>Category:</strong> {artifact.category || "Not available"}
                </Typography>
                <Typography>
                    <strong>Color:</strong> {artifact.color || "Not available"}
                </Typography>
                <Typography>
                    <strong>Size:</strong> {artifact.size || "Not available"}
                </Typography>
                <Typography>
                    <strong>Location:</strong> {displayCoordinates([artifact.lng, artifact.lat], !!user?.use_MGRS)}
                    <br />
                </Typography>
                <Typography>
                    <strong>Timestamp</strong>:{" "}
                    {artifact.timestamp ? dayjs(artifact.timestamp).tz(vesselTimezone).format(defaultValues.dateTimeFormat()) : "Not available"}{" "}
                    {vesselTimezone && simplifyTimezone(vesselTimezone)}
                </Typography>
                <Typography>{artifact.others || "Not available"}</Typography>
            </Grid>
        </Grid>
    );
};

const WrappedInfoWindow = (props) => (
    <UserProvider>
        <InfoWindow {...props} />
    </UserProvider>
);

export default WrappedInfoWindow;
