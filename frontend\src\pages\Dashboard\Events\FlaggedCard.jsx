import { Grid, Typography, Tooltip } from "@mui/material";
import { memo, useMemo, useEffect, useState } from "react";
import dayjs from "dayjs";
import { displayCoordinates, helperIconModes, userValues } from "../../../utils";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
// import { useApp } from "../../../hooks/AppHook";
import { useUser } from "../../../hooks/UserHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";
import RemoveFlagConfirmationModal from "../../../components/RemoveFlagConfirmationModal";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";

const FlaggedCard = ({ card, onFlaggedByClick, signedUrls }) => {
    const [src, setSrc] = useState(null);
    const { user } = useUser();
    const [thumbnail, setThumbnail] = useState(null);
    const { vesselInfo } = useVesselInfo();
    // const { timezone } = useApp();
    const [showRemoveModal, setShowRemoveModal] = useState(false);
    const [isRemoving, setIsRemoving] = useState(false);

    const roundedCoordinates = useMemo(
        () => displayCoordinates(card.artifact?.location?.coordinates, !!user?.use_MGRS),
        [card.artifact?.location?.coordinates, user?.use_MGRS],
    );

    const vessel = vesselInfo.find((v) => v.vessel_id === card.artifact?.onboard_vessel_id);
    const vesselName = vessel?.name || "Unknown Vessel";
    const isVideo = Boolean(card.artifact?.video_path);

    useEffect(() => {
        if (card.artifact) {
            if (signedUrls) {
                const thumbnailUrl = signedUrls.get(`${card.artifact._id}:thumbnail`);
                const imageUrl = signedUrls.get(`${card.artifact._id}:image`);
                const videoUrl = signedUrls.get(`${card.artifact._id}:video`);

                if (isVideo) {
                    setThumbnail(thumbnailUrl || imageUrl || null);
                    setSrc(videoUrl || null);
                } else {
                    setThumbnail(thumbnailUrl || imageUrl || null);
                    setSrc(imageUrl || null);
                }
            }
        }
    }, [card.artifact, isVideo, signedUrls]);

    const handleUnflagClick = (e) => {
        e.stopPropagation();
        setShowRemoveModal(true);
    };

    const handleRemoveConfirm = async (e) => {
        e.stopPropagation();
        setIsRemoving(true);
        try {
            await artifactFlagController.removeAllFlagsFromArtifact(card.artifact._id);
            setShowRemoveModal(false);
        } catch (error) {
            console.error("Error removing flags:", error);
        } finally {
            setIsRemoving(false);
        }
    };

    const handleRemoveCancel = (e) => {
        e.stopPropagation();
        setShowRemoveModal(false);
    };

    if (!card.artifact || !vesselInfo) return null;

    return (
        <Grid
            container
            paddingTop={"0 !important"}
            height={"100%"}
            maxHeight={"350px"}
            sx={{ cursor: "pointer" }}
            onClick={() => onFlaggedByClick(card.artifact)}
        >
            <Grid container backgroundColor={"primary.dark"} borderRadius={2} padding={1} gap={1}>
                <Grid size={12} maxHeight={"200px"}>
                    <PreviewMedia
                        thumbnailLink={thumbnail}
                        originalLink={src}
                        cardId={card.artifact._id}
                        isImage={!isVideo}
                        style={{ borderRadius: 8 }}
                        showVideoThumbnail={isVideo}
                        showArchiveButton={!card.artifact.is_archived}
                        isArchived={card.artifact.is_archived}
                        vesselId={card.artifact.onboard_vessel_id}
                        buttonsToShow={[helperIconModes.ARCHIVE]}
                        handleUnflagClick={handleUnflagClick}
                    />
                </Grid>
                <Grid container size={12}>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Tooltip title={vesselName.length > 12 ? vesselName : ""}>
                            <Typography fontSize={"14px"} fontWeight={500}>
                                {vesselName && (vesselName.length > 12 ? vesselName.slice(0, 12) + "..." : vesselName)}
                            </Typography>
                        </Tooltip>
                        <Typography fontSize={"14px"} fontWeight={500}>
                            {dayjs(card.artifact.timestamp)
                                // .tz(timezone)
                                .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Typography fontSize={"14px"} fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Location
                        </Typography>
                        <Typography fontSize={"14px"} fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Category
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Typography fontSize={"14px"} fontWeight={500} maxWidth={"50%"}>
                            {roundedCoordinates}
                        </Typography>
                        <Typography fontSize={"14px"} fontWeight={500} maxWidth={"50%"} textAlign={"right"}>
                            {card.artifact.super_category
                                ? card.artifact.super_category.length > 12
                                    ? card.artifact.super_category.slice(0, 12) + "..."
                                    : card.artifact.super_category
                                : "Unspecified category"}
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Typography
                            fontSize={"14px"}
                            fontWeight={500}
                            maxWidth={"50%"}
                            color="#FDBF2D"
                            fontFamily={`"Nunito Sans", sans-serif`}
                            sx={{ fontStyle: "italic", textDecoration: "underline" }}
                        >
                            Flagged by {card.flagCount || 0} {card.flagCount === 1 ? "user" : "users"}
                        </Typography>
                    </Grid>
                </Grid>
            </Grid>
            <RemoveFlagConfirmationModal open={showRemoveModal} onClose={handleRemoveCancel} onConfirm={handleRemoveConfirm} isLoading={isRemoving} />
        </Grid>
    );
};

export default memo(FlaggedCard);
